<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>QR Functionality Test</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .test-container {
            max-width: 800px;
            margin: 50px auto;
            padding: 20px;
        }
        .test-card {
            background: white;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            padding: 30px;
            margin-bottom: 20px;
        }
        .bundle-test-card {
            border: 2px solid #e9ecef;
            border-radius: 12px;
            padding: 20px;
            margin: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            display: inline-block;
            width: 150px;
            text-align: center;
        }
        .bundle-test-card:hover {
            border-color: #667eea;
            transform: translateY(-2px);
        }
        .qr-icon {
            position: absolute;
            top: 8px;
            left: 8px;
            font-size: 14px;
            color: #667eea;
            cursor: pointer;
            opacity: 0.7;
            transition: all 0.3s ease;
        }
        .qr-icon:hover {
            opacity: 1;
            transform: scale(1.1);
        }
        .status-indicator {
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 12px;
            font-weight: 500;
        }
        .status-success {
            background: #d4edda;
            color: #155724;
        }
        .status-warning {
            background: #fff3cd;
            color: #856404;
        }
        .status-error {
            background: #f8d7da;
            color: #721c24;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-card">
            <h2><i class="fas fa-flask me-2"></i>QR Functionality Test</h2>
            <p>This page tests the QR code functionality for the Bundle Management System.</p>
            
            <div class="row">
                <div class="col-md-6">
                    <h4>Application Status</h4>
                    <div id="app-status" class="status-indicator status-warning">
                        <i class="fas fa-spinner fa-spin me-1"></i>Checking...
                    </div>
                </div>
                <div class="col-md-6">
                    <h4>QR Generation Status</h4>
                    <div id="qr-status" class="status-indicator status-warning">
                        <i class="fas fa-spinner fa-spin me-1"></i>Testing...
                    </div>
                </div>
            </div>
        </div>

        <div class="test-card">
            <h4>Bundle Management Access</h4>
            <p>Test access to the bundle management system:</p>
            <div class="d-flex gap-3">
                <a href="http://127.0.0.1:5001/login" class="btn btn-primary" target="_blank">
                    <i class="fas fa-sign-in-alt me-1"></i>Login Page
                </a>
                <a href="http://127.0.0.1:5001/bundles" class="btn btn-success" target="_blank">
                    <i class="fas fa-layer-group me-1"></i>Bundle Management
                </a>
                <a href="http://127.0.0.1:5001/dashboard" class="btn btn-info" target="_blank">
                    <i class="fas fa-tachometer-alt me-1"></i>Dashboard
                </a>
            </div>
        </div>

        <div class="test-card">
            <h4>QR Code Test</h4>
            <p>Test QR code generation for sample bundles:</p>
            
            <div class="d-flex flex-wrap">
                <div class="bundle-test-card" onclick="testBundleQR(1)">
                    <div class="qr-icon" onclick="showTestQR(1, event)">
                        <i class="fas fa-qrcode"></i>
                    </div>
                    <div class="bundle-number">Bundle 1</div>
                    <div class="bundle-info">Compartment 1</div>
                </div>
                
                <div class="bundle-test-card" onclick="testBundleQR(150)">
                    <div class="qr-icon" onclick="showTestQR(150, event)">
                        <i class="fas fa-qrcode"></i>
                    </div>
                    <div class="bundle-number">Bundle 150</div>
                    <div class="bundle-info">Compartment 1</div>
                </div>
                
                <div class="bundle-test-card" onclick="testBundleQR(401)">
                    <div class="qr-icon" onclick="showTestQR(401, event)">
                        <i class="fas fa-qrcode"></i>
                    </div>
                    <div class="bundle-number">Bundle 401</div>
                    <div class="bundle-info">Compartment 2</div>
                </div>
                
                <div class="bundle-test-card" onclick="testBundleQR(800)">
                    <div class="qr-icon" onclick="showTestQR(800, event)">
                        <i class="fas fa-qrcode"></i>
                    </div>
                    <div class="bundle-number">Bundle 800</div>
                    <div class="bundle-info">Compartment 2</div>
                </div>
            </div>
        </div>

        <div class="test-card">
            <h4>Test Results</h4>
            <div id="test-results">
                <p class="text-muted">Click on QR icons above to test functionality...</p>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Test application status
        async function checkAppStatus() {
            try {
                const response = await fetch('http://127.0.0.1:5001/login');
                if (response.ok) {
                    document.getElementById('app-status').innerHTML = '<i class="fas fa-check me-1"></i>Running';
                    document.getElementById('app-status').className = 'status-indicator status-success';
                } else {
                    throw new Error('App not responding');
                }
            } catch (error) {
                document.getElementById('app-status').innerHTML = '<i class="fas fa-times me-1"></i>Not Running';
                document.getElementById('app-status').className = 'status-indicator status-error';
            }
        }

        // Test QR generation
        async function testQRGeneration() {
            try {
                const response = await fetch('http://127.0.0.1:5001/api/generate-bundle-qr/1', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                });
                
                if (response.ok) {
                    document.getElementById('qr-status').innerHTML = '<i class="fas fa-check me-1"></i>Working';
                    document.getElementById('qr-status').className = 'status-indicator status-success';
                } else {
                    throw new Error('QR generation failed');
                }
            } catch (error) {
                document.getElementById('qr-status').innerHTML = '<i class="fas fa-exclamation-triangle me-1"></i>Needs Login';
                document.getElementById('qr-status').className = 'status-indicator status-warning';
            }
        }

        function testBundleQR(bundleNum) {
            const compartment = bundleNum <= 400 ? 1 : 2;
            const rack = Math.floor((bundleNum - 1) / 40) + 1;
            
            addTestResult(`Bundle ${bundleNum} clicked - Compartment ${compartment}, Rack ${rack}`);
        }

        function showTestQR(bundleNum, event) {
            event.stopPropagation();
            
            const compartment = bundleNum <= 400 ? 1 : 2;
            const rack = Math.floor((bundleNum - 1) / 40) + 1;
            
            addTestResult(`QR icon clicked for Bundle ${bundleNum} - Would show QR modal`);
            
            // Test opening search interface
            const searchUrl = `http://127.0.0.1:5001/bundle-search-interface/${bundleNum}`;
            window.open(searchUrl, '_blank');
        }

        function addTestResult(message) {
            const resultsDiv = document.getElementById('test-results');
            const timestamp = new Date().toLocaleTimeString();
            const resultItem = document.createElement('div');
            resultItem.className = 'alert alert-info alert-sm';
            resultItem.innerHTML = `<strong>${timestamp}:</strong> ${message}`;
            
            if (resultsDiv.querySelector('.text-muted')) {
                resultsDiv.innerHTML = '';
            }
            
            resultsDiv.appendChild(resultItem);
        }

        // Run tests on page load
        document.addEventListener('DOMContentLoaded', function() {
            checkAppStatus();
            setTimeout(testQRGeneration, 1000);
        });
    </script>
</body>
</html>
